{
  "database": {
    "rules": "database.rules.json"
  },
  "flutter": {
    "platforms": {
      "android": {
        "default": {
          "projectId": "shaklabx",
          "appId": "1:815201363151:android:ad05cf8647791aa54ec3ba",
          "fileOutput": "android/app/google-services.json"
        }
      },
      "ios": {
        "default": {
          "projectId": "shaklabx",
          "appId": "1:815201363151:ios:7d7ea3106807098a4ec3ba",
          "uploadDebugSymbols": false,
          "fileOutput": "ios/Runner/GoogleService-Info.plist"
        }
      },
      "macos": {
        "default": {
          "projectId": "shaklabx",
          "appId": "1:815201363151:ios:7d7ea3106807098a4ec3ba",
          "uploadDebugSymbols": false,
          "fileOutput": "macos/Runner/GoogleService-Info.plist"
        }
      },
      "dart": {
        "lib/firebase_options.dart": {
          "projectId": "shaklabx",
          "configurations": {
            "android": "1:815201363151:android:ad05cf8647791aa54ec3ba",
            "ios": "1:815201363151:ios:7d7ea3106807098a4ec3ba",
            "macos": "1:815201363151:ios:7d7ea3106807098a4ec3ba",
            "web": "1:815201363151:web:de2adf9293b202be4ec3ba",
            "windows": "1:815201363151:web:5cb0221504d0c6ea4ec3ba"
          }
        }
      }
    }
  }
}}
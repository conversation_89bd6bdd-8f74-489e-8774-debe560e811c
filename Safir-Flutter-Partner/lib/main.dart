import 'dart:developer';

import 'package:animated_splash_screen/animated_splash_screen.dart';
import 'package:external_app_launcher/external_app_launcher.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'package:geolocator/geolocator.dart';
import 'package:page_transition/page_transition.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';
import 'package:workmanager/workmanager.dart';
import 'functions/functions.dart';
import 'functions/notifications.dart';
import 'pages/loadingPage/loading_view.dart';
import 'package:firebase_core/firebase_core.dart';
import '../../styles/styles.dart';

@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    // Your background task logic goes here
//  if(userData.userDetails.isNotEmpty){
//  GetUserData().currentPositionUpdate();
//  }
    try {
      await Firebase.initializeApp();
      var val = await Geolocator.getCurrentPosition();
      // ignore: prefer_typing_uninitialized_variables
      var id;
      if (inputData != null) {
        id = inputData['id'];
      }
      FirebaseDatabase.instance.ref().child('drivers/driver_$id').update({
        'lat-lng': val.latitude.toString(),
        'l': {'0': val.latitude, '1': val.longitude},
        'updated_at': ServerValue.timestamp
      });
      // ignore: empty_catches
    } catch (e) {}

    return Future.value(true);
  });
}

class MyOverlayContent extends StatefulWidget {
  const MyOverlayContent({super.key});

  @override
  State<MyOverlayContent> createState() => _MyOverlayContentState();
}

class _MyOverlayContentState extends State<MyOverlayContent> {
  @override
  void initState() {
    super.initState();
    // listen for any data from the main app
    FlutterOverlayWindow.overlayListener.listen((event) {
      log("Current Event: $event");
    });
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: GestureDetector(
        onLongPress: () async => await FlutterOverlayWindow.closeOverlay(),
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            GestureDetector(
              onTap: () async {
                await LaunchApp.openApp(
                  androidPackageName: 'com.safir.partner',
                  // iosUrlScheme: 'pulsesecure://',
                  // appStoreLink:
                  //     'itms-apps://itunes.apple.com/us/app/pulse-secure/id945832041',
                  // openStore: false
                );
                // platforms.invokeMethod('awakeapp');
              },
              child: CircleAvatar(
                backgroundColor: Colors.blue[700],
                radius: 50,
                child: Image.asset(AppAssets.logoWithoutBackground),
              ),
            ),
            Positioned(
              top: 0,
              right: 0,
              child: GestureDetector(
                onTap: () async => await FlutterOverlayWindow.closeOverlay(),
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// overlay entry point
@pragma("vm:entry-point")
void overlayMain() {
  runApp(
    const MaterialApp(
      debugShowCheckedModeBanner: false,
      home: MyOverlayContent(),
    ),
  );
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  await Firebase.initializeApp();
  initMessaging();
  checkInternetConnection();

  currentPositionUpdate();

  Workmanager().initialize(callbackDispatcher, isInDebugMode: false);

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  final platforms = const MethodChannel('flutter.app/awake');

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    Workmanager().cancelAll();
    super.initState();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.paused) {
      if (userDetails.isNotEmpty &&
          userDetails['role'] == 'driver' &&
          userDetails['active'] == true) {
        // service.startService();
        updateLocation(10);
        startOverlay();
      }
    }
    if (state == AppLifecycleState.resumed) {
      Workmanager().cancelAll();
      if (await FlutterOverlayWindow.isActive()) {
        await FlutterOverlayWindow.closeOverlay();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    platform = Theme.of(context).platform;

    return GestureDetector(
      onTap: () {
        //remove keyboard on touching anywhere on the screen.
        FocusScopeNode currentFocus = FocusScope.of(context);
        if (!currentFocus.hasPrimaryFocus) {
          currentFocus.unfocus();
          FocusManager.instance.primaryFocus?.unfocus();
        }
      },
      child: MaterialApp(
        debugShowCheckedModeBanner: false,
        title: 'ShaklabX Partner',
        theme: ThemeData(
          scaffoldBackgroundColor: page,
          fontFamily: 'Cairo',
          colorScheme: const ColorScheme.light(
            primary: Color(0xff2778A0),
          ),
        ),
        home: AnimatedSplashScreen(
          duration: 1200,
          splash: Image.asset(AppAssets.logoWithoutBackground),
          nextScreen: const LoadingView(),
          splashTransition: SplashTransition.slideTransition,
          pageTransitionType: PageTransitionType.fade,
          centered: true,
          splashIconSize: 196.0,
          backgroundColor: page,
          customTween:
              Tween<Offset>(begin: const Offset(-1, 0.0), end: Offset.zero),
          curve: Curves.easeOut,
          animationDuration: const Duration(milliseconds: 1000),
        ),
      ),
    );
  }
}

void startOverlay() async {
  if (await FlutterOverlayWindow.isActive()) {
    await FlutterOverlayWindow.closeOverlay();
  } else {
    await FlutterOverlayWindow.showOverlay(
      enableDrag: true,
      height: 175,
      width: 175,
      alignment: OverlayAlignment.topLeft,
      positionGravity: PositionGravity.left,
      startPosition: const OverlayPosition(20, 200),
    );
  }
}

void updateLocation(duration) {
  for (var i = 0; i < 15; i++) {
    Workmanager().registerPeriodicTask('locs_$i', 'update_locs_$i',
        initialDelay: Duration(minutes: i),
        frequency: const Duration(minutes: 15),
        constraints: Constraints(
            networkType: NetworkType.connected,
            requiresBatteryNotLow: false,
            requiresCharging: false,
            requiresDeviceIdle: false,
            requiresStorageNotLow: false),
        inputData: {'id': userDetails['id'].toString()});
  }
}
